{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.22"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5]}, {"build": "JUCE", "childIndexes": [2, 3], "hasInstallRule": true, "jsonFile": "directory-JUCE-Debug-583e590200587154fa82.json", "minimumCMakeVersion": {"string": "3.22"}, "parentIndex": 0, "projectIndex": 1, "source": "JUCE"}, {"build": "JUCE/modules", "hasInstallRule": true, "jsonFile": "directory-JUCE.modules-Debug-75e12007da05525a4629.json", "minimumCMakeVersion": {"string": "3.22"}, "parentIndex": 1, "projectIndex": 1, "source": "JUCE/modules"}, {"build": "JUCE/extras/Build", "childIndexes": [4], "hasInstallRule": true, "jsonFile": "directory-JUCE.extras.Build-Debug-3b65d93d5d984d19e246.json", "minimumCMakeVersion": {"string": "3.22"}, "parentIndex": 1, "projectIndex": 1, "source": "JUCE/extras/Build"}, {"build": "JUCE/extras/Build/juceaide", "hasInstallRule": true, "jsonFile": "directory-JUCE.extras.Build.juceaide-Debug-7030e125a0d661f24c2c.json", "minimumCMakeVersion": {"string": "3.22"}, "parentIndex": 3, "projectIndex": 1, "source": "JUCE/extras/Build/juceaide"}], "name": "Debug", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "COMPLI", "targetIndexes": [0, 1, 2, 3, 4, 5]}, {"directoryIndexes": [1, 2, 3, 4], "name": "JUCE", "parentIndex": 0}], "targets": [{"directoryIndex": 0, "id": "CompliAudioProcessor::@6890427a1f51a3e7e1df", "jsonFile": "target-CompliAudioProcessor-Debug-41e17bc73615ba653a82.json", "name": "CompliAudioProcessor", "projectIndex": 0}, {"directoryIndex": 0, "id": "CompliAudioProcessor_All::@6890427a1f51a3e7e1df", "jsonFile": "target-CompliAudioProcessor_All-Debug-e8e846028835f3010f6c.json", "name": "CompliAudioProcessor_All", "projectIndex": 0}, {"directoryIndex": 0, "id": "CompliAudioProcessor_Standalone::@6890427a1f51a3e7e1df", "jsonFile": "target-CompliAudioProcessor_Standalone-Debug-8243125b50bf16f78b9b.json", "name": "CompliAudioProcessor_Standalone", "projectIndex": 0}, {"directoryIndex": 0, "id": "CompliAudioProcessor_VST3::@6890427a1f51a3e7e1df", "jsonFile": "target-CompliAudioProcessor_VST3-Debug-71f444fd97a0d02974a2.json", "name": "CompliAudioProcessor_VST3", "projectIndex": 0}, {"directoryIndex": 0, "id": "CompliAudioProcessor_rc_lib::@6890427a1f51a3e7e1df", "jsonFile": "target-CompliAudioProcessor_rc_lib-Debug-d6b753410bcd08da0c79.json", "name": "CompliAudioProcessor_rc_lib", "projectIndex": 0}, {"directoryIndex": 0, "id": "juce_vst3_helper::@6890427a1f51a3e7e1df", "jsonFile": "target-juce_vst3_helper-Debug-a14943a067a95e089c7b.json", "name": "juce_vst3_helper", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug", "source": "C:/Users/<USER>/Documents/GitHub/compli2"}, "version": {"major": 2, "minor": 7}}