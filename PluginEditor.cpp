#include "PluginProcessor.h"
#include "PluginEditor.h"
#include "HeaderComponent.h"
#include "IoPresetComponent.h"
#include "ModuleSelectorComponent.h"
#include "CompressorControlsComponent.h"
#include "GateControlsComponent.h"       // Added
#include "AgcControlsComponent.h"        // Added
#include "DeesserControlsComponent.h"
#include "LimiterControlsComponent.h"
#include "LevelMonitoringComponent.h"    // Added
#include "FooterComponent.h"
#include "TabbedModuleSelectorComponent.h"

//==============================================================================
AudioPluginAudioProcessorEditor::AudioPluginAudioProcessorEditor (CompliAudioProcessor& p)
    : AudioProcessorEditor (&p),
      processorRef (p),
      headerComponent(),
      ioPresetComponent(p),
      tabbedModuleSelector(),
      compressorControls(p.apvts),
      gateControls(p.apvts),
      agcControls(p.apvts),
      deesserControls(p.apvts),
      limiterControls(p.apvts),
      levelMonitoringComponent(p),    // Added initialization
      footerComponent(p.apvts)
{
    // juce::ignoreUnused (processorRef); // Removed
    // Make sure that before the constructor has finished, you've set the
    // editor's size to whatever you need it to be.
    setSize (950, 750); // Increased height from 680 to 750 to provide more space for controls

    setLookAndFeel(&customLookAndFeel);

    addAndMakeVisible(headerComponent);
    addAndMakeVisible(ioPresetComponent);
    addAndMakeVisible(tabbedModuleSelector);

    // Set up tab selection callback
    tabbedModuleSelector.onTabSelected = [this](int tabIndex) {
        switchToModule(tabIndex);
    };

    addAndMakeVisible(compressorControls);
    addAndMakeVisible(gateControls);
    addAndMakeVisible(agcControls);
    addAndMakeVisible(deesserControls);
    addAndMakeVisible(limiterControls);
    addAndMakeVisible(levelMonitoringComponent);
    addAndMakeVisible(footerComponent);

    // Initial visibility - start with compressor (index 2)
    switchToModule(2);
}

AudioPluginAudioProcessorEditor::~AudioPluginAudioProcessorEditor()
{
    setLookAndFeel(nullptr); // Added
}

//==============================================================================
void AudioPluginAudioProcessorEditor::paint (juce::Graphics& g)
{
    // Background gradient matching the mockup
    auto bounds = getLocalBounds();
    juce::ColourGradient bgGradient(
        CustomLookAndFeel::primaryBackground, bounds.getTopLeft().toFloat(),
        juce::Colour(0xff1e293b), bounds.getBottomRight().toFloat(),
        true
    );
    g.setGradientFill(bgGradient);
    g.fillAll();
}

void AudioPluginAudioProcessorEditor::resized()
{
    auto bounds = getLocalBounds();
    bounds.reduce(16, 16); // Overall padding

    // Layout sections - adjusted heights to provide more space for controls
    auto headerHeight = 70;           // Reduced from 80
    auto ioPresetHeight = 100;        // Reduced from 120
    auto moduleSelectorHeight = 50;   // Reduced from 60
    auto footerHeight = 50;           // Reduced from 60
    auto levelMonitoringHeight = 160; // Reduced from 200

    // Header
    headerComponent.setBounds(bounds.removeFromTop(headerHeight));
    bounds.removeFromTop(6); // Reduced spacing

    // I/O and Preset section
    ioPresetComponent.setBounds(bounds.removeFromTop(ioPresetHeight));
    bounds.removeFromTop(6); // Reduced spacing

    // Module selector
    tabbedModuleSelector.setBounds(bounds.removeFromTop(moduleSelectorHeight));
    bounds.removeFromTop(6); // Reduced spacing

    // Footer
    auto footerBounds = bounds.removeFromBottom(footerHeight);
    footerComponent.setBounds(footerBounds);
    bounds.removeFromBottom(6); // Reduced spacing

    // Main content area - split between controls and level monitoring
    auto controlsArea = bounds;
    auto levelMonitoringBounds = controlsArea.removeFromBottom(levelMonitoringHeight);
    levelMonitoringComponent.setBounds(levelMonitoringBounds);

    controlsArea.removeFromBottom(6); // Reduced spacing

    // All control components use the same bounds (visibility controlled by module selector)
    compressorControls.setBounds(controlsArea);
    gateControls.setBounds(controlsArea);
    agcControls.setBounds(controlsArea);
    deesserControls.setBounds(controlsArea);
    limiterControls.setBounds(controlsArea);
}

void AudioPluginAudioProcessorEditor::switchToModule(int moduleIndex)
{
    // Hide all control panels
    compressorControls.setVisible(false);
    gateControls.setVisible(false);
    agcControls.setVisible(false);
    deesserControls.setVisible(false);
    limiterControls.setVisible(false);

    // Show selected module
    switch (moduleIndex)
    {
        case 0: // AGC
            agcControls.setVisible(true);
            break;
        case 1: // GATE
            gateControls.setVisible(true);
            break;
        case 2: // COMP
            compressorControls.setVisible(true);
            break;
        case 3: // DE-ESS
            deesserControls.setVisible(true);
            break;
        case 4: // LIMIT
            limiterControls.setVisible(true);
            break;
        default:
            compressorControls.setVisible(true); // Default to compressor
            break;
    }
}

void AudioPluginAudioProcessorEditor::comboBoxChanged(juce::ComboBox* comboBoxThatHasChanged)
{
    // This method is still needed for IoPresetComponent combo boxes
    juce::ignoreUnused(comboBoxThatHasChanged);
}
