{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "CompliAudioProcessor_artefacts/Release/compli_SharedCode.lib"}], "backtrace": 2, "backtraceGraph": {"commands": ["add_library", "juce_add_plugin", "target_link_libraries", "_juce_configure_plugin_targets", "target_compile_definitions", "_juce_add_plugin_definitions", "target_include_directories", "_juce_initialise_target", "target_sources", "juce_generate_juce_header"], "files": ["JUCE/extras/Build/CMake/JUCEUtils.cmake", "CMakeLists.txt", "JUCE/extras/Build/CMake/JUCEModuleSupport.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 40, "parent": 0}, {"command": 0, "file": 0, "line": 2179, "parent": 1}, {"command": 3, "file": 0, "line": 2182, "parent": 1}, {"command": 2, "file": 0, "line": 1490, "parent": 3}, {"command": 2, "file": 1, "line": 134, "parent": 0}, {"command": 4, "file": 0, "line": 1517, "parent": 3}, {"command": 4, "file": 0, "line": 1536, "parent": 3}, {"command": 4, "file": 1, "line": 112, "parent": 0}, {"command": 5, "file": 0, "line": 1508, "parent": 3}, {"command": 4, "file": 2, "line": 313, "parent": 9}, {"command": 4, "file": 2, "line": 313, "parent": 9}, {"command": 4, "file": 2, "line": 313, "parent": 9}, {"command": 4, "file": 2, "line": 313, "parent": 9}, {"command": 4, "file": 2, "line": 313, "parent": 9}, {"command": 4, "file": 2, "line": 313, "parent": 9}, {"command": 4, "file": 2, "line": 313, "parent": 9}, {"command": 4, "file": 2, "line": 313, "parent": 9}, {"command": 7, "file": 0, "line": 2181, "parent": 1}, {"command": 6, "file": 0, "line": 2100, "parent": 18}, {"command": 9, "file": 1, "line": 63, "parent": 0}, {"command": 8, "file": 0, "line": 559, "parent": 20}, {"command": 8, "file": 1, "line": 70, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD"}, {"backtrace": 4, "fragment": "/bigobj"}, {"backtrace": 5, "fragment": "/Ox"}, {"backtrace": 5, "fragment": "/MP"}, {"backtrace": 5, "fragment": "/EHsc"}, {"backtrace": 5, "fragment": "-GL"}, {"backtrace": 5, "fragment": "/W4"}], "defines": [{"backtrace": 4, "define": "JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_audio_basics=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_audio_devices=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_audio_formats=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_audio_processors=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_audio_utils=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_core=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_data_structures=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_dsp=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_events=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_graphics=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_gui_basics=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_gui_extra=1"}, {"backtrace": 6, "define": "JUCE_SHARED_CODE=1"}, {"backtrace": 7, "define": "JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone"}, {"backtrace": 8, "define": "JUCE_USE_CURL=0"}, {"backtrace": 8, "define": "JUCE_VST3_CAN_REPLACE_VST2=0"}, {"backtrace": 8, "define": "JUCE_WEB_BROWSER=0"}, {"backtrace": 7, "define": "JucePlugin_AAXCategory=0"}, {"backtrace": 7, "define": "JucePlugin_AAXDisableBypass=0"}, {"backtrace": 7, "define": "JucePlugin_AAXDisableMultiMono=0"}, {"backtrace": 7, "define": "JucePlugin_AAXIdentifier=com.Ferdmusic.CompliAudioProcessor"}, {"backtrace": 7, "define": "JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode"}, {"backtrace": 7, "define": "JucePlugin_AAXProductId=JucePlugin_PluginCode"}, {"backtrace": 7, "define": "JucePlugin_ARACompatibleArchiveIDs=\"\""}, {"backtrace": 7, "define": "JucePlugin_ARAContentTypes=0"}, {"backtrace": 7, "define": "JucePlugin_ARADocumentArchiveID=\"com.Ferdmusic.CompliAudioProcessor.aradocumentarchive.1\""}, {"backtrace": 7, "define": "JucePlugin_ARAFactoryID=\"com.Ferdmusic.CompliAudioProcessor.arafactory.0.0.1\""}, {"backtrace": 7, "define": "JucePlugin_ARATransformationFlags=0"}, {"backtrace": 7, "define": "JucePlugin_AUExportPrefix=compliAU"}, {"backtrace": 7, "define": "JucePlugin_AUExportPrefixQuoted=\"compliAU\""}, {"backtrace": 7, "define": "JucePlugin_AUMainType='aufx'"}, {"backtrace": 7, "define": "JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode"}, {"backtrace": 7, "define": "JucePlugin_AUSubType=JucePlugin_PluginCode"}, {"backtrace": 10, "define": "JucePlugin_Build_AAX=0"}, {"backtrace": 11, "define": "JucePlugin_Build_AU=0"}, {"backtrace": 12, "define": "JucePlugin_Build_AUv3=0"}, {"backtrace": 13, "define": "JucePlugin_Build_LV2=0"}, {"backtrace": 14, "define": "JucePlugin_Build_Standalone=1"}, {"backtrace": 15, "define": "JucePlugin_Build_Unity=0"}, {"backtrace": 16, "define": "JucePlugin_Build_VST3=1"}, {"backtrace": 17, "define": "JucePlugin_Build_VST=0"}, {"backtrace": 7, "define": "JucePlugin_CFBundleIdentifier=com.Ferdmusic.CompliAudioProcessor"}, {"backtrace": 7, "define": "JucePlugin_Desc=\"compli\""}, {"backtrace": 7, "define": "JucePlugin_EditorRequiresKeyboardFocus=0"}, {"backtrace": 7, "define": "JucePlugin_Enable_ARA=0"}, {"backtrace": 7, "define": "JucePlugin_IsMidiEffect=0"}, {"backtrace": 7, "define": "JucePlugin_IsSynth=0"}, {"backtrace": 7, "define": "JucePlugin_Manufacturer=\"Ferdmusic\""}, {"backtrace": 7, "define": "JucePlugin_ManufacturerCode=0x46657264"}, {"backtrace": 7, "define": "JucePlugin_ManufacturerEmail=\"\""}, {"backtrace": 7, "define": "JucePlugin_ManufacturerWebsite=\"\""}, {"backtrace": 7, "define": "JucePlugin_Name=\"compli\""}, {"backtrace": 7, "define": "JucePlugin_PluginCode=0x436d706c"}, {"backtrace": 7, "define": "JucePlugin_ProducesMidiOutput=0"}, {"backtrace": 7, "define": "JucePlugin_VSTCategory=kPlugCategEffect"}, {"backtrace": 7, "define": "JucePlugin_VSTNumMidiInputs=16"}, {"backtrace": 7, "define": "JucePlugin_VSTNumMidiOutputs=16"}, {"backtrace": 7, "define": "JucePlugin_VSTUniqueID=JucePlugin_PluginCode"}, {"backtrace": 7, "define": "JucePlugin_Version=0.0.1"}, {"backtrace": 7, "define": "JucePlugin_VersionCode=0x1"}, {"backtrace": 7, "define": "JucePlugin_VersionString=\"0.0.1\""}, {"backtrace": 7, "define": "JucePlugin_Vst3Category=\"Fx\""}, {"backtrace": 7, "define": "JucePlugin_WantsMidiInput=0"}, {"backtrace": 4, "define": "NDEBUG=1"}, {"backtrace": 4, "define": "_NDEBUG=1"}], "includes": [{"backtrace": 19, "path": "C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-release/CompliAudioProcessor_artefacts/JuceLibraryCode"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/VST3_SDK"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/lv2"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/serd"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/sord"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/sord/src"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/sratom"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/lilv"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/lilv/src"}], "language": "CXX", "languageStandard": {"backtraces": [4], "standard": "17"}, "sourceIndexes": [1, 2, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 34, 35, 36, 37, 38, 39, 40, 42, 43, 44, 45, 46, 47, 48, 49, 50]}, {"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /O2 /Ob2 /DNDEBUG -MD"}, {"backtrace": 4, "fragment": "/bigobj"}, {"backtrace": 5, "fragment": "/Ox"}, {"backtrace": 5, "fragment": "/MP"}, {"backtrace": 5, "fragment": "/EHsc"}, {"backtrace": 5, "fragment": "-GL"}, {"backtrace": 5, "fragment": "/W4"}], "defines": [{"backtrace": 4, "define": "JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_audio_basics=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_audio_devices=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_audio_formats=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_audio_processors=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_audio_utils=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_core=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_data_structures=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_dsp=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_events=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_graphics=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_gui_basics=1"}, {"backtrace": 4, "define": "JUCE_MODULE_AVAILABLE_juce_gui_extra=1"}, {"backtrace": 6, "define": "JUCE_SHARED_CODE=1"}, {"backtrace": 7, "define": "JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone"}, {"backtrace": 8, "define": "JUCE_USE_CURL=0"}, {"backtrace": 8, "define": "JUCE_VST3_CAN_REPLACE_VST2=0"}, {"backtrace": 8, "define": "JUCE_WEB_BROWSER=0"}, {"backtrace": 7, "define": "JucePlugin_AAXCategory=0"}, {"backtrace": 7, "define": "JucePlugin_AAXDisableBypass=0"}, {"backtrace": 7, "define": "JucePlugin_AAXDisableMultiMono=0"}, {"backtrace": 7, "define": "JucePlugin_AAXIdentifier=com.Ferdmusic.CompliAudioProcessor"}, {"backtrace": 7, "define": "JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode"}, {"backtrace": 7, "define": "JucePlugin_AAXProductId=JucePlugin_PluginCode"}, {"backtrace": 7, "define": "JucePlugin_ARACompatibleArchiveIDs=\"\""}, {"backtrace": 7, "define": "JucePlugin_ARAContentTypes=0"}, {"backtrace": 7, "define": "JucePlugin_ARADocumentArchiveID=\"com.Ferdmusic.CompliAudioProcessor.aradocumentarchive.1\""}, {"backtrace": 7, "define": "JucePlugin_ARAFactoryID=\"com.Ferdmusic.CompliAudioProcessor.arafactory.0.0.1\""}, {"backtrace": 7, "define": "JucePlugin_ARATransformationFlags=0"}, {"backtrace": 7, "define": "JucePlugin_AUExportPrefix=compliAU"}, {"backtrace": 7, "define": "JucePlugin_AUExportPrefixQuoted=\"compliAU\""}, {"backtrace": 7, "define": "JucePlugin_AUMainType='aufx'"}, {"backtrace": 7, "define": "JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode"}, {"backtrace": 7, "define": "JucePlugin_AUSubType=JucePlugin_PluginCode"}, {"backtrace": 10, "define": "JucePlugin_Build_AAX=0"}, {"backtrace": 11, "define": "JucePlugin_Build_AU=0"}, {"backtrace": 12, "define": "JucePlugin_Build_AUv3=0"}, {"backtrace": 13, "define": "JucePlugin_Build_LV2=0"}, {"backtrace": 14, "define": "JucePlugin_Build_Standalone=1"}, {"backtrace": 15, "define": "JucePlugin_Build_Unity=0"}, {"backtrace": 16, "define": "JucePlugin_Build_VST3=1"}, {"backtrace": 17, "define": "JucePlugin_Build_VST=0"}, {"backtrace": 7, "define": "JucePlugin_CFBundleIdentifier=com.Ferdmusic.CompliAudioProcessor"}, {"backtrace": 7, "define": "JucePlugin_Desc=\"compli\""}, {"backtrace": 7, "define": "JucePlugin_EditorRequiresKeyboardFocus=0"}, {"backtrace": 7, "define": "JucePlugin_Enable_ARA=0"}, {"backtrace": 7, "define": "JucePlugin_IsMidiEffect=0"}, {"backtrace": 7, "define": "JucePlugin_IsSynth=0"}, {"backtrace": 7, "define": "JucePlugin_Manufacturer=\"Ferdmusic\""}, {"backtrace": 7, "define": "JucePlugin_ManufacturerCode=0x46657264"}, {"backtrace": 7, "define": "JucePlugin_ManufacturerEmail=\"\""}, {"backtrace": 7, "define": "JucePlugin_ManufacturerWebsite=\"\""}, {"backtrace": 7, "define": "JucePlugin_Name=\"compli\""}, {"backtrace": 7, "define": "JucePlugin_PluginCode=0x436d706c"}, {"backtrace": 7, "define": "JucePlugin_ProducesMidiOutput=0"}, {"backtrace": 7, "define": "JucePlugin_VSTCategory=kPlugCategEffect"}, {"backtrace": 7, "define": "JucePlugin_VSTNumMidiInputs=16"}, {"backtrace": 7, "define": "JucePlugin_VSTNumMidiOutputs=16"}, {"backtrace": 7, "define": "JucePlugin_VSTUniqueID=JucePlugin_PluginCode"}, {"backtrace": 7, "define": "JucePlugin_Version=0.0.1"}, {"backtrace": 7, "define": "JucePlugin_VersionCode=0x1"}, {"backtrace": 7, "define": "JucePlugin_VersionString=\"0.0.1\""}, {"backtrace": 7, "define": "JucePlugin_Vst3Category=\"Fx\""}, {"backtrace": 7, "define": "JucePlugin_WantsMidiInput=0"}, {"backtrace": 4, "define": "NDEBUG=1"}, {"backtrace": 4, "define": "_NDEBUG=1"}], "includes": [{"backtrace": 19, "path": "C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-release/CompliAudioProcessor_artefacts/JuceLibraryCode"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/VST3_SDK"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/lv2"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/serd"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/sord"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/sord/src"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/sratom"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/lilv"}, {"backtrace": 4, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/lilv/src"}], "language": "C", "sourceIndexes": [41]}], "folder": {"name": "CompliAudioProcessor"}, "id": "CompliAudioProcessor::@6890427a1f51a3e7e1df", "name": "CompliAudioProcessor", "nameOnDisk": "compli_SharedCode.lib", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32]}, {"name": "Source Files", "sourceIndexes": [1, 2, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50]}, {"name": "CMake Rules", "sourceIndexes": [33]}], "sources": [{"backtrace": 21, "isGenerated": true, "path": "cmake-build-release/CompliAudioProcessor_artefacts/JuceLibraryCode/JuceHeader.h", "sourceGroupIndex": 0}, {"backtrace": 22, "compileGroupIndex": 0, "path": "PluginEditor.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "compileGroupIndex": 0, "path": "PluginProcessor.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "compileGroupIndex": 0, "path": "CustomLookAndFeel.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "path": "CustomLookAndFeel.h", "sourceGroupIndex": 0}, {"backtrace": 22, "compileGroupIndex": 0, "path": "HeaderComponent.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "path": "HeaderComponent.h", "sourceGroupIndex": 0}, {"backtrace": 22, "compileGroupIndex": 0, "path": "IoPresetComponent.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "path": "IoPresetComponent.h", "sourceGroupIndex": 0}, {"backtrace": 22, "compileGroupIndex": 0, "path": "ModuleSelectorComponent.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "path": "ModuleSelectorComponent.h", "sourceGroupIndex": 0}, {"backtrace": 22, "compileGroupIndex": 0, "path": "KnobWithLabels.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "path": "KnobWithLabels.h", "sourceGroupIndex": 0}, {"backtrace": 22, "compileGroupIndex": 0, "path": "CustomToggleButton.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "path": "CustomToggleButton.h", "sourceGroupIndex": 0}, {"backtrace": 22, "compileGroupIndex": 0, "path": "CompressorControlsComponent.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "path": "CompressorControlsComponent.h", "sourceGroupIndex": 0}, {"backtrace": 22, "compileGroupIndex": 0, "path": "GateControlsComponent.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "path": "GateControlsComponent.h", "sourceGroupIndex": 0}, {"backtrace": 22, "compileGroupIndex": 0, "path": "AgcControlsComponent.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "path": "AgcControlsComponent.h", "sourceGroupIndex": 0}, {"backtrace": 22, "compileGroupIndex": 0, "path": "DeesserControlsComponent.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "path": "DeesserControlsComponent.h", "sourceGroupIndex": 0}, {"backtrace": 22, "compileGroupIndex": 0, "path": "LimiterControlsComponent.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "path": "LimiterControlsComponent.h", "sourceGroupIndex": 0}, {"backtrace": 22, "compileGroupIndex": 0, "path": "LevelMeterComponent.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "path": "LevelMeterComponent.h", "sourceGroupIndex": 0}, {"backtrace": 22, "compileGroupIndex": 0, "path": "LevelMonitoringComponent.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "path": "LevelMonitoringComponent.h", "sourceGroupIndex": 0}, {"backtrace": 22, "compileGroupIndex": 0, "path": "FooterComponent.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "path": "FooterComponent.h", "sourceGroupIndex": 0}, {"backtrace": 22, "compileGroupIndex": 0, "path": "TabbedModuleSelectorComponent.cpp", "sourceGroupIndex": 1}, {"backtrace": 22, "path": "TabbedModuleSelectorComponent.h", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "cmake-build-release/CompliAudioProcessor_artefacts/JuceLibraryCode/JuceHeader.h.rule", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "JUCE/modules/juce_audio_processors/juce_audio_processors.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "JUCE/modules/juce_audio_processors/juce_audio_processors_ara.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "JUCE/modules/juce_audio_processors/juce_audio_processors_lv2_libs.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "JUCE/modules/juce_gui_extra/juce_gui_extra.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "JUCE/modules/juce_gui_basics/juce_gui_basics.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "JUCE/modules/juce_graphics/juce_graphics.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "JUCE/modules/juce_graphics/juce_graphics_Harfbuzz.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 1, "path": "JUCE/modules/juce_graphics/juce_graphics_Sheenbidi.c", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "JUCE/modules/juce_events/juce_events.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "JUCE/modules/juce_core/juce_core.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "JUCE/modules/juce_core/juce_core_CompilationTime.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "JUCE/modules/juce_data_structures/juce_data_structures.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "JUCE/modules/juce_audio_basics/juce_audio_basics.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "path": "JUCE/modules/juce_audio_utils/juce_audio_utils.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "path": "JUCE/modules/juce_audio_formats/juce_audio_formats.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "path": "JUCE/modules/juce_audio_devices/juce_audio_devices.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "path": "JUCE/modules/juce_dsp/juce_dsp.cpp", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}