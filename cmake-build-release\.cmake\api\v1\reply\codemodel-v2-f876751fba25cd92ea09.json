{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.22"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5]}, {"build": "JUCE", "childIndexes": [2, 3], "hasInstallRule": true, "jsonFile": "directory-JUCE-Release-0280a3d8baa3bb5ea251.json", "minimumCMakeVersion": {"string": "3.22"}, "parentIndex": 0, "projectIndex": 1, "source": "JUCE"}, {"build": "JUCE/modules", "hasInstallRule": true, "jsonFile": "directory-JUCE.modules-Release-75e12007da05525a4629.json", "minimumCMakeVersion": {"string": "3.22"}, "parentIndex": 1, "projectIndex": 1, "source": "JUCE/modules"}, {"build": "JUCE/extras/Build", "childIndexes": [4], "hasInstallRule": true, "jsonFile": "directory-JUCE.extras.Build-Release-3b65d93d5d984d19e246.json", "minimumCMakeVersion": {"string": "3.22"}, "parentIndex": 1, "projectIndex": 1, "source": "JUCE/extras/Build"}, {"build": "JUCE/extras/Build/juceaide", "hasInstallRule": true, "jsonFile": "directory-JUCE.extras.Build.juceaide-Release-f08948e344fbdaace0bb.json", "minimumCMakeVersion": {"string": "3.22"}, "parentIndex": 3, "projectIndex": 1, "source": "JUCE/extras/Build/juceaide"}], "name": "Release", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "COMPLI", "targetIndexes": [0, 1, 2, 3, 4, 5]}, {"directoryIndexes": [1, 2, 3, 4], "name": "JUCE", "parentIndex": 0}], "targets": [{"directoryIndex": 0, "id": "CompliAudioProcessor::@6890427a1f51a3e7e1df", "jsonFile": "target-CompliAudioProcessor-Release-ffc6869a1b351dc704e2.json", "name": "CompliAudioProcessor", "projectIndex": 0}, {"directoryIndex": 0, "id": "CompliAudioProcessor_All::@6890427a1f51a3e7e1df", "jsonFile": "target-CompliAudioProcessor_All-Release-2a5325fa37d0009ffe17.json", "name": "CompliAudioProcessor_All", "projectIndex": 0}, {"directoryIndex": 0, "id": "CompliAudioProcessor_Standalone::@6890427a1f51a3e7e1df", "jsonFile": "target-CompliAudioProcessor_Standalone-Release-c9e2ba051e30fd13061d.json", "name": "CompliAudioProcessor_Standalone", "projectIndex": 0}, {"directoryIndex": 0, "id": "CompliAudioProcessor_VST3::@6890427a1f51a3e7e1df", "jsonFile": "target-CompliAudioProcessor_VST3-Release-fe205aa10c8bfab0260e.json", "name": "CompliAudioProcessor_VST3", "projectIndex": 0}, {"directoryIndex": 0, "id": "CompliAudioProcessor_rc_lib::@6890427a1f51a3e7e1df", "jsonFile": "target-CompliAudioProcessor_rc_lib-Release-4481a75e651872c581e9.json", "name": "CompliAudioProcessor_rc_lib", "projectIndex": 0}, {"directoryIndex": 0, "id": "juce_vst3_helper::@6890427a1f51a3e7e1df", "jsonFile": "target-juce_vst3_helper-Release-171ee1fd0a37f13cc7f1.json", "name": "juce_vst3_helper", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-release", "source": "C:/Users/<USER>/Documents/GitHub/compli2"}, "version": {"major": 2, "minor": 7}}