{"artifacts": [{"path": "CompliAudioProcessor_artefacts/Release/Standalone/compli.exe"}, {"path": "CompliAudioProcessor_artefacts/Release/Standalone/compli.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_juce_link_plugin_wrapper", "_juce_configure_plugin_targets", "juce_add_plugin", "target_link_libraries", "include", "_juce_add_resources_rc", "target_include_directories"], "files": ["JUCE/extras/Build/CMake/JUCEUtils.cmake", "CMakeLists.txt", "JUCE/extras/Build/CMake/JUCEHelperTargets.cmake", "JUCE/extras/Build/CMake/JUCEModuleSupport.cmake", "JUCE/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 40, "parent": 0}, {"command": 2, "file": 0, "line": 2182, "parent": 1}, {"command": 1, "file": 0, "line": 1589, "parent": 2}, {"command": 0, "file": 0, "line": 1403, "parent": 3}, {"command": 4, "file": 0, "line": 1418, "parent": 3}, {"file": 4}, {"command": 5, "file": 4, "line": 50, "parent": 6}, {"file": 3, "parent": 7}, {"command": 5, "file": 3, "line": 56, "parent": 8}, {"file": 2, "parent": 9}, {"command": 4, "file": 2, "line": 156, "parent": 10}, {"command": 6, "file": 0, "line": 1453, "parent": 3}, {"command": 4, "file": 0, "line": 841, "parent": 12}, {"command": 7, "file": 0, "line": 1415, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD"}, {"backtrace": 5, "fragment": "/Ox"}, {"backtrace": 5, "fragment": "/MP"}, {"backtrace": 5, "fragment": "/EHsc"}, {"backtrace": 5, "fragment": "-GL"}, {"backtrace": 5, "fragment": "/W4"}], "defines": [{"backtrace": 5, "define": "JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_audio_basics=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_audio_devices=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_audio_formats=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_audio_plugin_client=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_audio_processors=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_audio_utils=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_core=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_data_structures=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_dsp=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_events=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_graphics=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_gui_basics=1"}, {"backtrace": 5, "define": "JUCE_MODULE_AVAILABLE_juce_gui_extra=1"}, {"backtrace": 5, "define": "JUCE_STANDALONE_APPLICATION=JucePlugin_Build_Standalone"}, {"backtrace": 5, "define": "JucePlugin_AAXCategory=0"}, {"backtrace": 5, "define": "JucePlugin_AAXDisableBypass=0"}, {"backtrace": 5, "define": "JucePlugin_AAXDisableMultiMono=0"}, {"backtrace": 5, "define": "JucePlugin_AAXIdentifier=com.Ferdmusic.CompliAudioProcessor"}, {"backtrace": 5, "define": "JucePlugin_AAXManufacturerCode=JucePlugin_ManufacturerCode"}, {"backtrace": 5, "define": "JucePlugin_AAXProductId=JucePlugin_PluginCode"}, {"backtrace": 5, "define": "JucePlugin_ARACompatibleArchiveIDs=\"\""}, {"backtrace": 5, "define": "JucePlugin_ARAContentTypes=0"}, {"backtrace": 5, "define": "JucePlugin_ARADocumentArchiveID=\"com.Ferdmusic.CompliAudioProcessor.aradocumentarchive.1\""}, {"backtrace": 5, "define": "JucePlugin_ARAFactoryID=\"com.Ferdmusic.CompliAudioProcessor.arafactory.0.0.1\""}, {"backtrace": 5, "define": "JucePlugin_ARATransformationFlags=0"}, {"backtrace": 5, "define": "JucePlugin_AUExportPrefix=compliAU"}, {"backtrace": 5, "define": "JucePlugin_AUExportPrefixQuoted=\"compliAU\""}, {"backtrace": 5, "define": "JucePlugin_AUMainType='aufx'"}, {"backtrace": 5, "define": "JucePlugin_AUManufacturerCode=JucePlugin_ManufacturerCode"}, {"backtrace": 5, "define": "JucePlugin_AUSubType=JucePlugin_PluginCode"}, {"backtrace": 5, "define": "JucePlugin_Build_AAX=0"}, {"backtrace": 5, "define": "JucePlugin_Build_AU=0"}, {"backtrace": 5, "define": "JucePlugin_Build_AUv3=0"}, {"backtrace": 5, "define": "JucePlugin_Build_LV2=0"}, {"backtrace": 5, "define": "JucePlugin_Build_Standalone=1"}, {"backtrace": 5, "define": "JucePlugin_Build_Unity=0"}, {"backtrace": 5, "define": "JucePlugin_Build_VST3=0"}, {"backtrace": 5, "define": "JucePlugin_Build_VST=0"}, {"backtrace": 5, "define": "JucePlugin_CFBundleIdentifier=com.Ferdmusic.CompliAudioProcessor"}, {"backtrace": 5, "define": "JucePlugin_Desc=\"compli\""}, {"backtrace": 5, "define": "JucePlugin_EditorRequiresKeyboardFocus=0"}, {"backtrace": 5, "define": "JucePlugin_Enable_ARA=0"}, {"backtrace": 5, "define": "JucePlugin_IsMidiEffect=0"}, {"backtrace": 5, "define": "JucePlugin_IsSynth=0"}, {"backtrace": 5, "define": "JucePlugin_Manufacturer=\"Ferdmusic\""}, {"backtrace": 5, "define": "JucePlugin_ManufacturerCode=0x46657264"}, {"backtrace": 5, "define": "JucePlugin_ManufacturerEmail=\"\""}, {"backtrace": 5, "define": "JucePlugin_ManufacturerWebsite=\"\""}, {"backtrace": 5, "define": "JucePlugin_Name=\"compli\""}, {"backtrace": 5, "define": "JucePlugin_PluginCode=0x436d706c"}, {"backtrace": 5, "define": "JucePlugin_ProducesMidiOutput=0"}, {"backtrace": 5, "define": "JucePlugin_VSTCategory=kPlugCategEffect"}, {"backtrace": 5, "define": "JucePlugin_VSTNumMidiInputs=16"}, {"backtrace": 5, "define": "JucePlugin_VSTNumMidiOutputs=16"}, {"backtrace": 5, "define": "JucePlugin_VSTUniqueID=JucePlugin_PluginCode"}, {"backtrace": 5, "define": "JucePlugin_Version=0.0.1"}, {"backtrace": 5, "define": "JucePlugin_VersionCode=0x1"}, {"backtrace": 5, "define": "JucePlugin_VersionString=\"0.0.1\""}, {"backtrace": 5, "define": "JucePlugin_Vst3Category=\"Fx\""}, {"backtrace": 5, "define": "JucePlugin_WantsMidiInput=0"}, {"backtrace": 5, "define": "NDEBUG=1"}, {"backtrace": 5, "define": "_NDEBUG=1"}], "includes": [{"backtrace": 14, "path": "C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-release/CompliAudioProcessor_artefacts/JuceLibraryCode"}, {"backtrace": 14, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules"}, {"backtrace": 14, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/VST3_SDK"}, {"backtrace": 14, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK"}, {"backtrace": 14, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/lv2"}, {"backtrace": 14, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/serd"}, {"backtrace": 14, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/sord"}, {"backtrace": 14, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/sord/src"}, {"backtrace": 14, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/sratom"}, {"backtrace": 14, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/lilv"}, {"backtrace": 14, "path": "C:/Users/<USER>/Documents/GitHub/compli2/JUCE/modules/juce_audio_processors/format_types/LV2_SDK/lilv/src"}], "language": "CXX", "languageStandard": {"backtraces": [5], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "dependencies": [{"backtrace": 5, "id": "CompliAudioProcessor::@6890427a1f51a3e7e1df"}, {"backtrace": 13, "id": "CompliAudioProcessor_rc_lib::@6890427a1f51a3e7e1df"}], "folder": {"name": "CompliAudioProcessor"}, "id": "CompliAudioProcessor_Standalone::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -MD", "role": "flags"}, {"fragment": "/machine:x64 /INCREMENTAL:NO /subsystem:windows", "role": "flags"}, {"backtrace": 5, "fragment": "CompliAudioProcessor_artefacts\\Release\\compli_SharedCode.lib", "role": "libraries"}, {"backtrace": 11, "fragment": "-LTCG", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "CompliAudioProcessor_Standalone", "nameOnDisk": "compli.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}, {"name": "Object Libraries", "sourceIndexes": [8]}], "sources": [{"backtrace": 5, "compileGroupIndex": 0, "path": "JUCE/modules/juce_audio_plugin_client/juce_audio_plugin_client_AAX.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "path": "JUCE/modules/juce_audio_plugin_client/juce_audio_plugin_client_AAX_utils.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "path": "JUCE/modules/juce_audio_plugin_client/juce_audio_plugin_client_ARA.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "path": "JUCE/modules/juce_audio_plugin_client/juce_audio_plugin_client_LV2.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "path": "JUCE/modules/juce_audio_plugin_client/juce_audio_plugin_client_Standalone.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "path": "JUCE/modules/juce_audio_plugin_client/juce_audio_plugin_client_Unity.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "path": "JUCE/modules/juce_audio_plugin_client/juce_audio_plugin_client_VST2.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "path": "JUCE/modules/juce_audio_plugin_client/juce_audio_plugin_client_VST3.cpp", "sourceGroupIndex": 0}, {"backtrace": 13, "isGenerated": true, "path": "cmake-build-release/CMakeFiles/CompliAudioProcessor_rc_lib.dir/CompliAudioProcessor_artefacts/JuceLibraryCode/CompliAudioProcessor_resources.rc.res", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}