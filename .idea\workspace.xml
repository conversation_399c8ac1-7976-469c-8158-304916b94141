<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="BackendCodeEditorMiscSettings">
    <option name="/Default/Housekeeping/FeatureSuggestion/FeatureSuggestionManager/DisabledSuggesters/=SwitchToGoToActionSuggester/@EntryIndexedValue" value="true" type="bool" />
    <option name="/Default/Housekeeping/RefactoringsMru/RenameRefactoring/DoSearchForTextInStrings/@EntryValue" value="true" type="bool" />
  </component>
  <component name="CMakePresetLoader">{
  &quot;useNewFormat&quot;: true
}</component>
  <component name="CMakeProjectFlavorService">
    <option name="flavorId" value="CMakePlainProjectFlavor" />
  </component>
  <component name="CMakeReloadState">
    <option name="reloaded" value="true" />
  </component>
  <component name="CMakeRunConfigurationManager">
    <generated>
      <config projectName="COMPLI" targetName="juce_vst3_helper" />
      <config projectName="COMPLI" targetName="CompliAudioProcessor_All" />
      <config projectName="COMPLI" targetName="CompliAudioProcessor_rc_lib" />
      <config projectName="COMPLI" targetName="CompliAudioProcessor_VST3" />
      <config projectName="COMPLI" targetName="CompliAudioProcessor_Standalone" />
      <config projectName="COMPLI" targetName="CompliAudioProcessor" />
    </generated>
  </component>
  <component name="CMakeSettings">
    <configurations>
      <configuration PROFILE_NAME="Debug" ENABLED="true" CONFIG_NAME="Debug" />
      <configuration PROFILE_NAME="Release" ENABLED="true" CONFIG_NAME="Release" />
    </configurations>
  </component>
  <component name="ChangeListManager">
    <list default="true" id="fff29d33-f1f9-4399-885c-121bbeb2d73b" name="Changes" comment="Enhance audio device management by adding &quot;None&quot; option for input/output devices and implementing change listener for dynamic updates" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="CMakeBuildProfile:Debug" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="ROOT_SYNC" value="DONT_SYNC" />
  </component>
  <component name="GitHubPullRequestSearchHistory"><![CDATA[{
  "lastFilter": {
    "state": "OPEN",
    "assignee": "ferdmusic"
  }
}]]></component>
  <component name="GithubPullRequestsUISettings"><![CDATA[{
  "selectedUrlAndAccountId": {
    "url": "https://github.com/juce-framework/JUCE",
    "accountId": "dfde5559-4102-4575-95d4-3c5a12785ab6"
  }
}]]></component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/CustomLookAndFeel.cpp" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/CustomLookAndFeel.h" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/JUCE/modules/juce_audio_plugin_client/Standalone/juce_StandaloneFilterWindow.h" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/PluginEditor.cpp" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/PluginEditor.h" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/PluginProcessor.cpp" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/PluginProcessor.h" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="ProjectErrors" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2xoH3vp9PNdfgGFRdOaayqfLn2T" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "CMake Application.AudioPluginExample_Standalone.executor": "Run",
    "CMake Application.CompliAudioProcessor_Standalone.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.RadMigrateCodeStyle": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "RunOnceActivity.west.config.association.type.startup.service": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "git-widget-placeholder": "main",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "C:/Users/<USER>/Documents/GitHub/juce-learning",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "run.code.analysis.last.selected.profile": "pProject Default",
    "settings.editor.selected.configurable": "CMakeSettings",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Documents\GitHub\juce-learning" />
    </key>
  </component>
  <component name="RunManager" selected="CMake Application.CompliAudioProcessor_All">
    <configuration name="CompliAudioProcessor" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="COMPLI" TARGET_NAME="CompliAudioProcessor" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="CompliAudioProcessor_All" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="COMPLI" TARGET_NAME="CompliAudioProcessor_All" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="CompliAudioProcessor_Standalone" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="COMPLI" TARGET_NAME="CompliAudioProcessor_Standalone" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="COMPLI" RUN_TARGET_NAME="CompliAudioProcessor_Standalone">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="CompliAudioProcessor_VST3" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="COMPLI" TARGET_NAME="CompliAudioProcessor_VST3" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="CompliAudioProcessor_rc_lib" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="COMPLI" TARGET_NAME="CompliAudioProcessor_rc_lib" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="juce_vst3_helper" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="COMPLI" TARGET_NAME="juce_vst3_helper" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="COMPLI" RUN_TARGET_NAME="juce_vst3_helper">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="CMake Application.CompliAudioProcessor" />
      <item itemvalue="CMake Application.CompliAudioProcessor_All" />
      <item itemvalue="CMake Application.CompliAudioProcessor_rc_lib" />
      <item itemvalue="CMake Application.CompliAudioProcessor_Standalone" />
      <item itemvalue="CMake Application.CompliAudioProcessor_VST3" />
      <item itemvalue="CMake Application.juce_vst3_helper" />
    </list>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="fff29d33-f1f9-4399-885c-121bbeb2d73b" name="Changes" comment="" />
      <created>1748597311737</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748597311737</updated>
      <workItem from="1748597313719" duration="170000" />
      <workItem from="1748597528665" duration="5209000" />
      <workItem from="1748606795126" duration="16729000" />
    </task>
    <task id="LOCAL-00001" summary="Update project configuration and state management">
      <option name="closed" value="true" />
      <created>1748598259733</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1748598259734</updated>
    </task>
    <task id="LOCAL-00002" summary="NOT WORKING!">
      <option name="closed" value="true" />
      <created>1748619686068</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1748619686068</updated>
    </task>
    <task id="LOCAL-00003" summary="Refactor CMakeLists and enhance UI components with glassmorphism design">
      <option name="closed" value="true" />
      <created>1748623848699</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1748623848699</updated>
    </task>
    <task id="LOCAL-00004" summary="Enhance audio device management by adding &quot;None&quot; option for input/output devices and implementing change listener for dynamic updates">
      <option name="closed" value="true" />
      <created>1748625147462</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1748625147462</updated>
    </task>
    <option name="localTasksCounter" value="5" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VCPKGProject">
    <isAutomaticCheckingOnLaunch value="false" />
    <isAutomaticFoundErrors value="true" />
    <isAutomaticReloadCMake value="true" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Update project configuration and state management" />
    <MESSAGE value="NOT WORKING!" />
    <MESSAGE value="Refactor CMakeLists and enhance UI components with glassmorphism design" />
    <MESSAGE value="Enhance audio device management by adding &quot;None&quot; option for input/output devices and implementing change listener for dynamic updates" />
    <option name="LAST_COMMIT_MESSAGE" value="Enhance audio device management by adding &quot;None&quot; option for input/output devices and implementing change listener for dynamic updates" />
  </component>
</project>