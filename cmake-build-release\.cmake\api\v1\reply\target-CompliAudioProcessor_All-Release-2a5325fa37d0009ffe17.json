{"backtrace": 3, "backtraceGraph": {"commands": ["add_custom_target", "_juce_configure_plugin_targets", "juce_add_plugin", "add_dependencies", "_juce_link_plugin_wrapper"], "files": ["JUCE/extras/Build/CMake/JUCEUtils.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 2, "file": 1, "line": 40, "parent": 0}, {"command": 1, "file": 0, "line": 2182, "parent": 1}, {"command": 0, "file": 0, "line": 1585, "parent": 2}, {"command": 4, "file": 0, "line": 1589, "parent": 2}, {"command": 3, "file": 0, "line": 1444, "parent": 4}, {"command": 4, "file": 0, "line": 1589, "parent": 2}, {"command": 3, "file": 0, "line": 1444, "parent": 6}]}, "dependencies": [{"backtrace": 5, "id": "CompliAudioProcessor_VST3::@6890427a1f51a3e7e1df"}, {"backtrace": 7, "id": "CompliAudioProcessor_Standalone::@6890427a1f51a3e7e1df"}], "folder": {"name": "CompliAudioProcessor"}, "id": "CompliAudioProcessor_All::@6890427a1f51a3e7e1df", "name": "CompliAudioProcessor_All", "paths": {"build": ".", "source": "."}, "sources": [], "type": "UTILITY"}