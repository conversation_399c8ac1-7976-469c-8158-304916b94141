{"artifacts": [{"path": "CMakeFiles/CompliAudioProcessor_rc_lib.dir/CompliAudioProcessor_artefacts/JuceLibraryCode/CompliAudioProcessor_resources.rc.res"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "_juce_add_resources_rc", "_juce_link_plugin_wrapper", "_juce_configure_plugin_targets", "juce_add_plugin"], "files": ["JUCE/extras/Build/CMake/JUCEUtils.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 4, "file": 1, "line": 40, "parent": 0}, {"command": 3, "file": 0, "line": 2182, "parent": 1}, {"command": 2, "file": 0, "line": 1589, "parent": 2}, {"command": 1, "file": 0, "line": 1453, "parent": 3}, {"command": 0, "file": 0, "line": 828, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DWIN32"}], "language": "RC", "sourceIndexes": [0]}], "id": "CompliAudioProcessor_rc_lib::@6890427a1f51a3e7e1df", "name": "CompliAudioProcessor_rc_lib", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "cmake-build-release/CompliAudioProcessor_artefacts/JuceLibraryCode/CompliAudioProcessor_resources.rc", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "cmake-build-release/CompliAudioProcessor_artefacts/JuceLibraryCode/CompliAudioProcessor_resources.rc.rule", "sourceGroupIndex": 1}], "type": "OBJECT_LIBRARY"}